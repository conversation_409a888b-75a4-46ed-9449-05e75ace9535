<testsuites id="" name="" tests="81" failures="4" skipped="77" errors="0" time="225.241339">
<testsuite name="e2e\hardware-specific.spec.js" timestamp="2025-10-01T11:15:42.653Z" hostname="chromium" tests="9" failures="4" skipped="5" time="173.152" errors="0">
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › images should display correctly in Samsung portrait mode" classname="e2e\hardware-specific.spec.js" time="41.576">
<failure message="hardware-specific.spec.js:11:9 images should display correctly in Samsung portrait mode" type="FAILURE">
<![CDATA[  [chromium] › e2e\hardware-specific.spec.js:11:9 › Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › images should display correctly in Samsung portrait mode 

    Test timeout of 30000ms exceeded.

    Error: page.goto: Test timeout of 30000ms exceeded.
    Call log:
      - navigating to "http://localhost:3000/", waiting until "load"


      13 |       test.skip(browserName === 'webkit' && process.platform === 'darwin', 'iOS Safari has different viewport behavior');
      14 |       
    > 15 |       await page.goto('/');
         |                  ^
      16 |       
      17 |       // Set Samsung Galaxy S9+ portrait viewport
      18 |       await page.setViewportSize({ width: 320, height: 658 });
        at C:\Users\<USER>\source\repos\tutorScotland\tests\e2e\hardware-specific.spec.js:15:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\e2e-hardware-specific-Hard-72993-ly-in-Samsung-portrait-mode-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\e2e-hardware-specific-Hard-72993-ly-in-Samsung-portrait-mode-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\e2e-hardware-specific-Hard-72993-ly-in-Samsung-portrait-mode-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-hardware-specific-Hard-72993-ly-in-Samsung-portrait-mode-chromium\test-failed-1.png]]

[[ATTACHMENT|e2e-hardware-specific-Hard-72993-ly-in-Samsung-portrait-mode-chromium\video.webm]]

[[ATTACHMENT|e2e-hardware-specific-Hard-72993-ly-in-Samsung-portrait-mode-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › nested HTML images render correctly in Samsung portrait" classname="e2e\hardware-specific.spec.js" time="40.07">
<failure message="hardware-specific.spec.js:58:9 nested HTML images render correctly in Samsung portrait" type="FAILURE">
<![CDATA[  [chromium] › e2e\hardware-specific.spec.js:58:9 › Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › nested HTML images render correctly in Samsung portrait 

    Test timeout of 30000ms exceeded.

    Error: expect(locator).toBeVisible()

    Locator: locator('div img, section img, article img').first()
    Expected: visible
    Received: hidden
    Call log:
      - Expect "toBeVisible" with timeout 10000ms
      - waiting for locator('div img, section img, article img').first()
        7 × locator resolved to <img id="imageShield" class="main-shield" alt="Large TAS Shield" src="/images/centralShield.png" data-ve-block-id="d2163d6b-8a53-4262-824e-5ade6019f412"/>
          - unexpected value "hidden"


      80 |           
      81 |           // Ensure parent isn't clipping the image
    > 82 |           await expect(img).toBeVisible();
         |                             ^
      83 |           
      84 |           // Check image positioning
      85 |           const boundingBox = await img.boundingBox();
        at C:\Users\<USER>\source\repos\tutorScotland\tests\e2e\hardware-specific.spec.js:82:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\e2e-hardware-specific-Hard-f062d-rrectly-in-Samsung-portrait-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\e2e-hardware-specific-Hard-f062d-rrectly-in-Samsung-portrait-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\e2e-hardware-specific-Hard-f062d-rrectly-in-Samsung-portrait-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-hardware-specific-Hard-f062d-rrectly-in-Samsung-portrait-chromium\test-failed-1.png]]

[[ATTACHMENT|e2e-hardware-specific-Hard-f062d-rrectly-in-Samsung-portrait-chromium\video.webm]]

[[ATTACHMENT|e2e-hardware-specific-Hard-f062d-rrectly-in-Samsung-portrait-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › team member sections display correctly on Samsung portrait" classname="e2e\hardware-specific.spec.js" time="45.761">
<failure message="hardware-specific.spec.js:93:9 team member sections display correctly on Samsung portrait" type="FAILURE">
<![CDATA[  [chromium] › e2e\hardware-specific.spec.js:93:9 › Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › team member sections display correctly on Samsung portrait 

    Test timeout of 30000ms exceeded.

    Error: page.waitForLoadState: Test timeout of 30000ms exceeded.

       96 |
       97 |       // Wait for page to load and responsive helper to apply fixes
    >  98 |       await page.waitForLoadState('networkidle');
          |                  ^
       99 |       await page.waitForTimeout(1000); // Allow time for Samsung fix to apply
      100 |
      101 |       // Check team members container
        at C:\Users\<USER>\source\repos\tutorScotland\tests\e2e\hardware-specific.spec.js:98:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\e2e-hardware-specific-Hard-150cc-rrectly-on-Samsung-portrait-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\e2e-hardware-specific-Hard-150cc-rrectly-on-Samsung-portrait-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\e2e-hardware-specific-Hard-150cc-rrectly-on-Samsung-portrait-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-hardware-specific-Hard-150cc-rrectly-on-Samsung-portrait-chromium\test-failed-1.png]]

[[ATTACHMENT|e2e-hardware-specific-Hard-150cc-rrectly-on-Samsung-portrait-chromium\video.webm]]

[[ATTACHMENT|e2e-hardware-specific-Hard-150cc-rrectly-on-Samsung-portrait-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › iOS Safari Viewport Issues › viewport height units work correctly on iOS" classname="e2e\hardware-specific.spec.js" time="1.928">
<properties>
<property name="skip" value="iOS-specific test">
</property>
</properties>
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › High-DPI Display Issues › images scale correctly on high-DPI displays" classname="e2e\hardware-specific.spec.js" time="43.817">
<failure message="hardware-specific.spec.js:182:9 images scale correctly on high-DPI displays" type="FAILURE">
<![CDATA[  [chromium] › e2e\hardware-specific.spec.js:182:9 › Hardware-Specific Rendering Tests › High-DPI Display Issues › images scale correctly on high-DPI displays 

    Test timeout of 30000ms exceeded.

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\e2e-hardware-specific-Hard-c73fd-rectly-on-high-DPI-displays-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\e2e-hardware-specific-Hard-c73fd-rectly-on-high-DPI-displays-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\e2e-hardware-specific-Hard-c73fd-rectly-on-high-DPI-displays-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-hardware-specific-Hard-c73fd-rectly-on-high-DPI-displays-chromium\test-failed-1.png]]

[[ATTACHMENT|e2e-hardware-specific-Hard-c73fd-rectly-on-high-DPI-displays-chromium\video.webm]]

[[ATTACHMENT|e2e-hardware-specific-Hard-c73fd-rectly-on-high-DPI-displays-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Touch vs Mouse Interaction › touch events work correctly on mobile devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Browser-Specific CSS Support › CSS Grid works across all browsers" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › homepage renders consistently across devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › admin panel renders correctly on tablets" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e\hardware-specific.spec.js" timestamp="2025-10-01T11:15:42.653Z" hostname="firefox" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › images should display correctly in Samsung portrait mode" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › nested HTML images render correctly in Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › team member sections display correctly on Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › iOS Safari Viewport Issues › viewport height units work correctly on iOS" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › High-DPI Display Issues › images scale correctly on high-DPI displays" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Touch vs Mouse Interaction › touch events work correctly on mobile devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Browser-Specific CSS Support › CSS Grid works across all browsers" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › homepage renders consistently across devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › admin panel renders correctly on tablets" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e\hardware-specific.spec.js" timestamp="2025-10-01T11:15:42.653Z" hostname="webkit" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › images should display correctly in Samsung portrait mode" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › nested HTML images render correctly in Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › team member sections display correctly on Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › iOS Safari Viewport Issues › viewport height units work correctly on iOS" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › High-DPI Display Issues › images scale correctly on high-DPI displays" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Touch vs Mouse Interaction › touch events work correctly on mobile devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Browser-Specific CSS Support › CSS Grid works across all browsers" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › homepage renders consistently across devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › admin panel renders correctly on tablets" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e\hardware-specific.spec.js" timestamp="2025-10-01T11:15:42.653Z" hostname="Mobile Chrome" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › images should display correctly in Samsung portrait mode" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › nested HTML images render correctly in Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › team member sections display correctly on Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › iOS Safari Viewport Issues › viewport height units work correctly on iOS" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › High-DPI Display Issues › images scale correctly on high-DPI displays" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Touch vs Mouse Interaction › touch events work correctly on mobile devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Browser-Specific CSS Support › CSS Grid works across all browsers" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › homepage renders consistently across devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › admin panel renders correctly on tablets" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e\hardware-specific.spec.js" timestamp="2025-10-01T11:15:42.653Z" hostname="Mobile Safari" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › images should display correctly in Samsung portrait mode" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › nested HTML images render correctly in Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › team member sections display correctly on Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › iOS Safari Viewport Issues › viewport height units work correctly on iOS" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › High-DPI Display Issues › images scale correctly on high-DPI displays" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Touch vs Mouse Interaction › touch events work correctly on mobile devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Browser-Specific CSS Support › CSS Grid works across all browsers" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › homepage renders consistently across devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › admin panel renders correctly on tablets" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e\hardware-specific.spec.js" timestamp="2025-10-01T11:15:42.653Z" hostname="Samsung Galaxy" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › images should display correctly in Samsung portrait mode" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › nested HTML images render correctly in Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › team member sections display correctly on Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › iOS Safari Viewport Issues › viewport height units work correctly on iOS" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › High-DPI Display Issues › images scale correctly on high-DPI displays" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Touch vs Mouse Interaction › touch events work correctly on mobile devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Browser-Specific CSS Support › CSS Grid works across all browsers" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › homepage renders consistently across devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › admin panel renders correctly on tablets" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e\hardware-specific.spec.js" timestamp="2025-10-01T11:15:42.653Z" hostname="iPhone Portrait" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › images should display correctly in Samsung portrait mode" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › nested HTML images render correctly in Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › team member sections display correctly on Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › iOS Safari Viewport Issues › viewport height units work correctly on iOS" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › High-DPI Display Issues › images scale correctly on high-DPI displays" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Touch vs Mouse Interaction › touch events work correctly on mobile devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Browser-Specific CSS Support › CSS Grid works across all browsers" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › homepage renders consistently across devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › admin panel renders correctly on tablets" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e\hardware-specific.spec.js" timestamp="2025-10-01T11:15:42.653Z" hostname="Samsung Portrait" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › images should display correctly in Samsung portrait mode" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › nested HTML images render correctly in Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › team member sections display correctly on Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › iOS Safari Viewport Issues › viewport height units work correctly on iOS" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › High-DPI Display Issues › images scale correctly on high-DPI displays" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Touch vs Mouse Interaction › touch events work correctly on mobile devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Browser-Specific CSS Support › CSS Grid works across all browsers" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › homepage renders consistently across devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › admin panel renders correctly on tablets" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="e2e\hardware-specific.spec.js" timestamp="2025-10-01T11:15:42.653Z" hostname="iPad" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › images should display correctly in Samsung portrait mode" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › nested HTML images render correctly in Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Samsung Portrait Viewport Issues › team member sections display correctly on Samsung portrait" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › iOS Safari Viewport Issues › viewport height units work correctly on iOS" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › High-DPI Display Issues › images scale correctly on high-DPI displays" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Touch vs Mouse Interaction › touch events work correctly on mobile devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Hardware-Specific Rendering Tests › Browser-Specific CSS Support › CSS Grid works across all browsers" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › homepage renders consistently across devices" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression - Hardware Specific › admin panel renders correctly on tablets" classname="e2e\hardware-specific.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>